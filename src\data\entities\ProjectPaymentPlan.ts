import { Info } from '../FieldTypes'
import { TenantEntity, TenantEntityInfo } from './TenantEntity'
import { ProjectContract } from './ProjectContract'
import { round } from '../../services/utils'
import { ProjectPaymentFact } from './ProjectPaymentFact'

export interface ProjectPaymentPlan extends TenantEntity {
  projectId: string
  name: string
  contractId?: string
  payerId: string
  payeeId: string
  category?: string
  payMethod?: string
  currency: string
  description?: string
  dueDate: Date
  percentage: number
  amount: number
  taxRate: number
  taxAmount: number
  totalAmount: number
  projectPaymentFacts?: ProjectPaymentFact[]
}

export const Categories = ['Advance', 'Milestone', 'Progress', 'Final', 'Other'] as const
export type Category = (typeof Categories)[number]
export const PayMethods = ['Bank Transfer', 'Cheque', 'Online', 'Cash', 'Other'] as const
export type SPayMethod = (typeof PayMethods)[number]

export const projectPaymentPlanInfo: Info<ProjectPaymentPlan> = {
  typeName: 'Project Payment Plan',
  nameKey: 'name',
  sortKey: 'name',
  sortAsc: true,
  backend: 'Project',
  endpoint: 'ProjectPaymentPlan',
  fields: {
    projectId: { label: 'Project', type: 'external', required: true },
    name: { label: 'Plan Name', type: 'smalltext', required: true },
    contractId: { label: 'Contract', type: 'external' },
    payerId: { label: 'Payer', type: 'external', required: true },
    payeeId: { label: 'Payee', type: 'external', required: true },
    dueDate: { label: 'Due Date', type: 'date', required: true },
    category: { label: 'Category', type: 'select' },
    payMethod: { label: 'Payment Method', type: 'select' },
    currency: { label: 'Currency', type: 'codetype', required: true },
    amount: { label: 'Contract Amount', type: 'currency', required: true },
    percentage: { label: 'Pay Percentage', type: 'percent', required: true },
    taxRate: { label: 'Tax Rate', type: 'percent', required: true },
    taxAmount: { label: 'Tax Amount', type: 'currency', disabled: true },
    totalAmount: { label: 'Total Pay Amount', type: 'currency', disabled: true },
    description: { label: 'Description', type: 'textarea' },
    projectPaymentFacts: { label: 'Payment Facts', type: 'objlist' },
    ...TenantEntityInfo.fields,
  },
  options: {
    projectId: { entity: 'Project' },
    contractId: {
      entity: 'ProjectContract',
      joins: { projectId: 'projectId' },
      sync: (x, extern?) => {
        if (!extern?.value) return
        const projectContract = extern.value as ProjectContract
        x.value.amount = projectContract.amount
        x.value.payerId = projectContract.relatedPartyA
        x.value.payeeId = projectContract.relatedPartyB
        x.value.currency = projectContract.currency
        x.value.taxRate = projectContract.taxRate
      },
    },
    payerId: { entity: 'ProjectRelatedParty', joins: { projectId: 'projectId' } },
    payeeId: { entity: 'ProjectRelatedParty', joins: { projectId: 'projectId' } },
    category: { options: Categories.slice() },
    payMethod: { options: PayMethods.slice() },
    currency: { typeCode: 'Currency' },
    percentage: {
      sync: (x) => {
        x.value.taxAmount = round(x.value.amount * x.value.percentage * 0.01 * x.value.taxRate * 0.01)
        x.value.totalAmount = round(x.value.amount * x.value.percentage * 0.01 + x.value.taxAmount)
      },
    },
    amount: {
      currencyKey: 'currency',
      sync: (x) => {
        x.value.taxAmount = round(x.value.amount * x.value.percentage * 0.01 * x.value.taxRate * 0.01)
        x.value.totalAmount = round(x.value.amount * x.value.percentage * 0.01 + x.value.taxAmount)
      },
    },
    taxRate: {
      sync: (x) => (x.value.taxAmount = round(x.value.amount * x.value.percentage * 0.01 * x.value.taxRate * 0.01)),
    },
    taxAmount: {
      currencyKey: 'currency',
      sync: (x) => (x.value.totalAmount = round(x.value.amount * x.value.percentage * 0.01 + x.value.taxAmount)),
    },
    totalAmount: { currencyKey: 'currency' },
    projectPaymentFacts: { entity: 'ProjectPaymentFact' },
    ...TenantEntityInfo.options,
  },
  default: {
    projectId: '',
    name: '',
    contractId: '',
    payerId: '',
    payeeId: '',
    category: '',
    payMethod: '',
    currency: 'CAD',
    dueDate: new Date(),
    percentage: 100,
    amount: 0,
    taxRate: 0,
    taxAmount: 0,
    totalAmount: 0,
  },
  columnsShown: new Set([
    'projectId',
    'name',
    'contractId',
    'payerId',
    'payeeId',
    'dueDate',
    'category',
    'payMethod',
    'percentage',
    'amount',
    'totalAmount',
    'description',
  ]),
  formLayout: [
    ['projectId', 'name', 'contractId'],
    ['payerId', 'payeeId'],
    ['category', 'payMethod'],
    ['currency', 'dueDate'],
    ['amount', 'percentage'],
    ['taxRate', 'taxAmount'],
    ['totalAmount'],
    ['description'],
  ],
  tabList: {
    ProjectPaymentFact: { tabName: 'Payment Facts', relation: { projectId: 'projectId', id: 'payPlanId' } },
  },
}
